// 处理拖拽的文件路径（包括文件夹）
ipcMain.handle('files:processDroppedPaths', async (_, paths: string[], workflowId?: string) => {
  const startTime = performance.now();
  const allItems: AppFile[] = [];
  // 从设置中获取处理上限，默认为1000
  const MAX_ITEMS = store.get('workflow.processing.maxItems', 1000) as number;

  console.log(`开始处理拖拽文件，路径数量: ${paths.length}, 工作流ID: ${workflowId}`);

  // 获取工作流设置以确定是否包含子文件夹和处理目标
  let includeSubfolders = true; // 默认包含子文件夹
  let processTarget: 'files' | 'folders' = 'files'; // 默认只处理文件

  if (workflowId) {
    try {
      const workflows = await loadWorkflows();
      const workflow = workflows.find(w => w.id === workflowId);
      if (workflow) {
        includeSubfolders = workflow.includeSubfolders !== false;
        // 如果工作流有步骤，使用第一个步骤的处理目标
        if (workflow.steps.length > 0) {
          processTarget = workflow.steps[0].processTarget || 'files';
        }
      }
    } catch (error) {
      console.warn('Failed to load workflow settings, using defaults', error);
    }
  }